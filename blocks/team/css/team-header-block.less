//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.teamHeaderBlock {
  min-height: 100vh;
  padding: @vw100 + @vw40 0;
  position: relative;
  &.inview {
    .contactDetails {
      .button {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.3s 0.75s ease-out, transform 0.3s 0.75s ease-out;
        .stagger(20, 0.15s, .45s);
      }
    }
  }
  .contentWrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
  }

  .backToTeam {
    position: absolute;
    top: @vw40;
    left: 0;
    color: @hardWhite;
    
    .arrows {
      color: @secondaryColorLight;
    }
    
    &:hover {
      .arrows {
        color: @hardWhite;
      }
    }
  }

  .memberPhotoWrapper {
    margin-bottom: @vw80;
    pointer-events: none;
    .pulse{
      position: absolute;
      animation: pulse-wave 4s linear infinite both;
      .rounded(@vw6);
      border: solid 1px @secondaryColorLight;
      position: absolute;
      top: 50%;
      left: 50%;
      height: 100%;
      width: 100%;
      .transform(translate(-50%, -50%) scale(1));
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1s;
      }
      &:nth-child(3) {
        animation-delay: 2s;
      }
    }
    .memberPhoto {
      width: @vw100 + @vw30;
      height: @vw100 + @vw30;
      .rounded(@vw6);
      border: 1px solid @secondaryColorLight;
      margin: 0 auto;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
      }
    }
  }

  .normalTitle {
    margin-bottom: @vw10;
  }

  .contactDetails {
    margin: auto;
    display: block;
    width: (@vw106 * 4) + (@vw16 * 3);
    max-width: 100%;
    margin-top: @vw50;
    .button {
      display: flex;
      .transform(translateY(@vw20));
      opacity: 0;
      &:not(:last-child) {
        margin-bottom: @vw12;
      }
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .teamHeaderBlock {
    padding: (@vw100 * 1.5) 0 (@vw100 * 0.5);
    
    .memberPhotoWrapper {
      margin: (@vw100 * 1) 0 @vw40 0;
      
      .memberPhoto {
        width: (@vw100 * 1.5);
        height: (@vw100 * 1.5);
      }
    }
    
    .memberName {
      font-size: (@vw100 * 0.8);
      margin-bottom: (@vw100 * 0.7);
    }
    
    .contactDetails {
      max-width: 100%;
      padding: 0 @vw20;
      
      .contactItem {
        padding: @vw16 @vw20;
        
        .contactText {
          font-size: @vw14;
        }
        
        .contactIcon {
          width: @vw20;
          height: @vw20;
          
          i {
            font-size: @vw18;
          }
        }
      }
    }
  }
}

@keyframes pulse-wave{
  0%{
    opacity: .3;
    .transform(translate(-50%, -50%) scale(1));
  }
  100%{
    opacity: 0;
    .transform(translate(-50%, -50%) scale(2));
  }
}