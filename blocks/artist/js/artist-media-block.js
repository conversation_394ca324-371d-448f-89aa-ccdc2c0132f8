$(document).ready(function () {
    $(document).on("initPage", function () {
        if ($(".artistMediaBlock").length > 0) {
            initializeArtistMediaBlock();
        }
    });
});

function initializeArtistMediaBlock() {
    const $block = $('.artistMediaBlock');
    
    // Check for iframe loading errors and provide fallbacks
    $block.find('iframe').each(function() {
        const $iframe = $(this);
        const $container = $iframe.closest('.spotifyContainer, .youtubeContainer');
        let hasLoaded = false;

        // Listen for successful load
        $iframe.on('load', function() {
            hasLoaded = true;
            // Only log for debugging if needed
            // console.log('Media embed loaded successfully');
        });

        // Listen for iframe load errors
        $iframe.on('error', function() {
            handleIframeError($iframe, $container);
        });

        // Set a timeout to check if iframe loaded
        setTimeout(() => {
            if (!hasLoaded) {
                try {
                    // Try to access iframe content (will fail if blocked by CORS/DRM)
                    const iframeDoc = $iframe[0].contentDocument || $iframe[0].contentWindow.document;

                    // If we can't access it, it might be blocked
                    if (!iframeDoc) {
                        handleIframeError($iframe, $container);
                    }
                } catch (e) {
                    // CORS/DRM error - iframe is blocked (this is normal for some embeds)
                    handleIframeError($iframe, $container);
                }
            }
        }, 5000); // Increased timeout to 5 seconds
    });
}

function handleIframeError($iframe, $container) {
    // Show fallback option for better user experience

    // For Spotify containers, show a fallback
    if ($container.hasClass('spotifyContainer')) {
        const spotifyUrl = $iframe.attr('src');

        if (spotifyUrl) {
            // Convert embed URL back to regular Spotify URL
            const regularUrl = spotifyUrl
                .replace('open.spotify.com/embed/', 'open.spotify.com/')
                .split('?')[0]; // Remove query parameters

            const fallbackHtml = `
                <div class="spotify-fallback">
                    <div class="fallback-message">
                        <p>Listen on Spotify</p>
                        <p><small>Open this playlist in the Spotify app</small></p>
                        <a href="${regularUrl}" target="_blank" class="button">
                            <span class="innerText">Open on Spotify</span>
                            <span class="arrows">
                                <i class="icon-arrow-right-up"></i>
                                <i class="icon-arrow-right-up"></i>
                            </span>
                        </a>
                    </div>
                </div>
            `;

            $container.html(fallbackHtml);
        }
    }

    // For YouTube containers, show a fallback
    if ($container.hasClass('youtubeContainer')) {
        const youtubeUrl = $iframe.attr('src');

        if (youtubeUrl) {
            // Extract video ID and create regular YouTube URL
            const videoIdMatch = youtubeUrl.match(/embed\/([^?]+)/);
            if (videoIdMatch) {
                const videoId = videoIdMatch[1];
                const regularUrl = `https://www.youtube.com/watch?v=${videoId}`;

                const fallbackHtml = `
                    <div class="youtube-fallback">
                        <div class="fallback-message">
                            <p>Watch on YouTube</p>
                            <p><small>Open this video in YouTube</small></p>
                            <a href="${regularUrl}" target="_blank" class="button">
                                <span class="innerText">Open on YouTube</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                `;

                $container.html(fallbackHtml);
            }
        }
    }
}

// Force HTTPS on any remaining HTTP links
$(document).ready(function() {
    $('iframe[src^="http://"]').each(function() {
        const $iframe = $(this);
        const src = $iframe.attr('src');
        $iframe.attr('src', src.replace('http://', 'https://'));
    });
});
